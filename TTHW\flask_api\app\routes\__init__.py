# app/routes/__init__.py

from .export_summary import bp as export_summary_bp
from .etl_api import bp as etl_api_bp  # ETL API 蓝图
from .upload_page import bp as upload_page_bp  # 上传页面蓝图
# 数据库版本路由
from .customer_summary_db import bp as customer_summary_db_bp
from .order_summary_db import bp as order_summary_db_bp
from .get_order_details_db import bp as get_order_details_db_bp
from .delete_order_db import bp as delete_order_db_bp
from .filter_orders_db import bp as filter_orders_db_bp
from .filter_data_db import bp as filter_data_db_bp
from .filter_overdue_orders_db import bp as filter_overdue_orders_db_bp
from .summary_data_db import bp as summary_data_db_bp
from .overdue_summary import bp as overdue_summary_bp


def register_blueprints(app):
    app.register_blueprint(export_summary_bp)
    app.register_blueprint(etl_api_bp)  # 注册 ETL API 蓝图
    app.register_blueprint(upload_page_bp)  # 注册上传页面蓝图
    # 注册数据库版本路由
    app.register_blueprint(customer_summary_db_bp)
    app.register_blueprint(order_summary_db_bp)
    app.register_blueprint(get_order_details_db_bp)
    app.register_blueprint(delete_order_db_bp)
    app.register_blueprint(filter_orders_db_bp)
    app.register_blueprint(filter_data_db_bp)
    app.register_blueprint(filter_overdue_orders_db_bp)
    app.register_blueprint(summary_data_db_bp)
    app.register_blueprint(overdue_summary_bp)
