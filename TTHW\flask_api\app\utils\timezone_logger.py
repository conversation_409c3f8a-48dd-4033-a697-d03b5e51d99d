"""
时区感知的日志配置工具
解决Docker容器中日志时间显示问题
"""
import logging
import os
from datetime import datetime
import pytz


class TimezoneFormatter(logging.Formatter):
    """带时区的日志格式化器"""
    
    def __init__(self, fmt=None, datefmt=None, timezone='Asia/Shanghai'):
        super().__init__(fmt, datefmt)
        self.timezone = pytz.timezone(timezone)
    
    def formatTime(self, record, datefmt=None):
        """格式化时间为指定时区"""
        dt = datetime.fromtimestamp(record.created, tz=self.timezone)
        if datefmt:
            return dt.strftime(datefmt)
        else:
            return dt.strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]


def setup_timezone_logger(name, log_file=None, level=logging.INFO,
                          timezone='Asia/Shanghai'):
    """
    设置带时区的日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径（可选）
        level: 日志级别
        timezone: 时区名称，默认为中国时区
    
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除已有的处理器，避免重复设置
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = TimezoneFormatter(
        fmt='%(asctime)s [%(levelname)s] %(message)s',
        timezone=timezone
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_current_time_info():
    """获取当前时间信息，用于调试时区问题"""
    utc_time = datetime.utcnow()
    china_tz = pytz.timezone('Asia/Shanghai')
    china_time = utc_time.replace(tzinfo=pytz.UTC).astimezone(china_tz)
    
    return {
        'utc_time': utc_time.strftime('%Y-%m-%d %H:%M:%S UTC'),
        'china_time': china_time.strftime('%Y-%m-%d %H:%M:%S CST'),
        'system_timezone': str(datetime.now().astimezone().tzinfo),
        'container_timezone': os.environ.get('TZ', 'Not set')
    }
