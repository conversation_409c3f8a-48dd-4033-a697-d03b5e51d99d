# app/utils/date_parser.py

from datetime import datetime, date
import logging

def parse_date(date_value, value_name="日期"):
    """
    尝试将一个值解析为日期对象。

    :param date_value: 要解析的值，可以是字符串、datetime或date对象。
    :param value_name: 值的名称，用于错误提示（默认为"日期"）。
    :return: datetime.date 对象或 None
    """
    if not date_value:
        return None
    try:
        if isinstance(date_value, datetime):
            return date_value.date()
        elif isinstance(date_value, date):
            return date_value
        else:
            return datetime.strptime(str(date_value).strip(), "%Y-%m-%d").date()
    except Exception:
        logging.warning(f"{value_name}格式不正确：{date_value}")
        return None
