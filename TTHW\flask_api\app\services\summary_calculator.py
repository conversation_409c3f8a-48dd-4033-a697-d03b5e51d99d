# app/services/summary_calculator.py
"""
汇总数据计算层
实现复杂的业务计算逻辑，包括周期查询和累计查询的不同算法
"""

import logging

from typing import List, Dict, Tuple
from decimal import Decimal
from collections import defaultdict

from app.services.dto import (
    ShopSummaryData, OrderAggregateData, TransactionAggregateData, 
    OverdueData
)
from app.services.config import SummaryConfig

logger = logging.getLogger(__name__)


class SummaryCalculator:
    """汇总数据计算器"""
    
    def __init__(self):
        self.config = SummaryConfig
    
    def calculate_period_summary(
        self, 
        order_data: List[OrderAggregateData],
        transaction_data: List[TransactionAggregateData],
        unassigned_cost: Decimal,
        period_repayments: Dict[int, Decimal] = None
    ) -> Dict[str, ShopSummaryData]:
        """
        计算周期汇总数据
        注意：周期查询有特殊的待收金额计算逻辑
        """
        logger.info("开始计算周期汇总数据")
        
        # 初始化店铺数据
        shop_summary = self._initialize_shop_summary()
        
        # 处理订单数据
        self._process_order_data(shop_summary, order_data, is_period=True)
        
        # 处理交易数据
        self._process_transaction_data(shop_summary, transaction_data)
        
        # 分配未分配成本
        self._distribute_unassigned_cost(shop_summary, unassigned_cost)
        
        # 计算业绩和实际出资
        self._calculate_performance_and_investment(shop_summary, order_data, transaction_data)
        
        # 计算总平台数据
        total_platform = self._calculate_total_platform(shop_summary)
        shop_summary["总平台"] = total_platform
        
        logger.info("周期汇总数据计算完成")
        return shop_summary
    
    def calculate_cumulative_summary(
        self,
        order_data: List[OrderAggregateData],
        transaction_data: List[TransactionAggregateData],
        unassigned_cost: Decimal
    ) -> Dict[str, ShopSummaryData]:
        """
        计算累计汇总数据
        累计查询直接使用current_receivable字段
        """
        logger.info("开始计算累计汇总数据")
        
        # 初始化店铺数据
        shop_summary = self._initialize_shop_summary()
        
        # 处理订单数据
        self._process_order_data(shop_summary, order_data, is_period=False)
        
        # 处理交易数据
        self._process_transaction_data(shop_summary, transaction_data)
        
        # 分配未分配成本
        self._distribute_unassigned_cost(shop_summary, unassigned_cost)
        
        # 计算业绩和实际出资
        self._calculate_performance_and_investment(shop_summary, order_data, transaction_data)
        
        # 计算总平台数据
        total_platform = self._calculate_total_platform(shop_summary)
        shop_summary["总平台"] = total_platform
        
        logger.info("累计汇总数据计算完成")
        return shop_summary
    
    def apply_overdue_data(
        self,
        shop_summary: Dict[str, ShopSummaryData],
        overdue_data: List[OverdueData]
    ) -> Dict[str, ShopSummaryData]:
        """
        应用逾期数据到汇总结果
        逾期数据是独立计算的，不受时间段限制
        """
        logger.info("开始应用逾期数据")
        
        # 创建逾期数据映射
        overdue_map = {data.shop_affiliation: data for data in overdue_data}
        
        # 应用逾期数据到各店铺
        for shop_name in list(shop_summary.keys()):
            if shop_name in overdue_map:
                overdue = overdue_map[shop_name]
                shop_summary[shop_name].overdue_principal = overdue.overdue_principal
                shop_summary[shop_name].overdue_receivable = overdue.overdue_receivable
                shop_summary[shop_name].overdue_orders = overdue.overdue_count
        
        logger.info("逾期数据应用完成")
        return shop_summary
    
    def _initialize_shop_summary(self) -> Dict[str, ShopSummaryData]:
        """初始化店铺汇总数据"""
        return {
            shop: ShopSummaryData(shop_name=shop)
            for shop in self.config.SHOP_NAMES
        }
    
    def _process_order_data(
        self,
        shop_summary: Dict[str, ShopSummaryData],
        order_data: List[OrderAggregateData],
        is_period: bool
    ):
        """处理订单数据"""
        for order in order_data:
            shop = order.shop_affiliation
            if shop not in shop_summary:
                continue
            
            shop_data = shop_summary[shop]
            
            # 累加台数
            shop_data.total_devices += order.total_devices
            
            # 判断产品类型
            is_ecommerce = self.config.is_ecommerce_product(order.product_type)
            
            # 统计订单数
            if is_ecommerce:
                shop_data.ecommerce_orders += order.total_devices
            else:
                shop_data.lease_orders += order.total_devices
            
            # 根据订单状态处理
            if order.status == self.config.COMPLETED_ORDER_STATUS:
                shop_data.completed_orders += order.total_devices
            elif order.status == self.config.OVERDUE_ORDER_STATUS:
                shop_data.overdue_orders += order.total_devices
                if order.overdue_principal:
                    shop_data.overdue_principal += order.overdue_principal
                shop_data.overdue_receivable += order.current_receivable
            
            # 处理业绩数据（所有状态的订单都计入业绩）
            if is_ecommerce:
                shop_data.ecommerce_performance += order.total_receivable
            else:
                shop_data.lease_performance += order.total_receivable
            
            # 处理待收数据（只有在途和逾期状态）
            if order.status in self.config.ACTIVE_ORDER_STATUSES:
                if is_period:
                    # 周期查询：使用total_receivable（后续会根据还款调整）
                    if is_ecommerce:
                        shop_data.ecommerce_receivable += order.total_receivable
                    else:
                        shop_data.lease_receivable += order.total_receivable
                else:
                    # 累计查询：直接使用current_receivable
                    if is_ecommerce:
                        shop_data.ecommerce_receivable += order.current_receivable
                    else:
                        shop_data.lease_receivable += order.current_receivable
            
            # 累加订单成本（注意：成本来源于"放款"类型交易聚合，不是订单表的cost字段）
            if order.cost:
                shop_data.cost += order.cost  # 订单成本来源于放款交易聚合
        
        # 计算总待收
        for shop_data in shop_summary.values():
            shop_data.total_receivable = shop_data.lease_receivable + shop_data.ecommerce_receivable
    
    def _process_transaction_data(
        self,
        shop_summary: Dict[str, ShopSummaryData],
        transaction_data: List[TransactionAggregateData]
    ):
        """处理交易数据"""
        # 用于跟踪增值费和延保服务的分类
        ecommerce_additional_fees = defaultdict(lambda: defaultdict(Decimal))
        lease_additional_fees = defaultdict(lambda: defaultdict(Decimal))
        
        for trans in transaction_data:
            shop = trans.shop_affiliation
            if shop not in shop_summary:
                continue
            
            shop_data = shop_summary[shop]
            category = self.config.get_transaction_category(trans.transaction_type)
            
            if not category:
                continue
            
            amount = trans.total_amount
            
            # 处理支出类型（使用绝对值）
            if self.config.is_expense_category(category):
                amount = abs(amount)
            
            # 分配到对应字段
            if category == "增值费":
                shop_data.value_added_fee += amount
                self._distribute_additional_fee(
                    trans, amount, "增值费", ecommerce_additional_fees, lease_additional_fees
                )
            elif category == "延保服务":
                shop_data.extended_warranty += amount
                self._distribute_additional_fee(
                    trans, amount, "延保服务", ecommerce_additional_fees, lease_additional_fees
                )
            elif category == "首付款":
                shop_data.down_payment += amount
            elif category == "租金":
                shop_data.rent += amount
            elif category == "尾款":
                shop_data.final_payment += amount
            elif category == "放款":
                shop_data.loan_amount += amount
            elif category == "复投":
                shop_data.reinvestment += amount
            elif category == "供应商利润":
                shop_data.supplier_profit += amount
            # 成本类别已在订单成本或未分配成本中体现，此处不再累加

    def _distribute_additional_fee(
        self,
        trans: TransactionAggregateData,
        amount: Decimal,
        fee_type: str,
        ecommerce_fees: Dict,
        lease_fees: Dict
    ):
        """分配增值费和延保服务到电商/租赁"""
        shop = trans.shop_affiliation
        
        if trans.order_product_type:
            is_ecommerce = self.config.is_ecommerce_product(trans.order_product_type)
            if is_ecommerce:
                ecommerce_fees[shop][fee_type] += amount
            else:
                lease_fees[shop][fee_type] += amount
        else:
            # 无法确定类型，平均分配
            ecommerce_fees[shop][fee_type] += amount / 2
            lease_fees[shop][fee_type] += amount / 2
    
    def _distribute_unassigned_cost(
        self,
        shop_summary: Dict[str, ShopSummaryData],
        unassigned_cost: Decimal
    ):
        """兼容旧版：在 Calculator 内不再分摊未分配成本，由 SummaryService 统一处理。"""
        # 不进行任何分摊，直接返回以避免多计成本
        return


    def _calculate_performance_and_investment(
        self,
        shop_summary: Dict[str, ShopSummaryData],
        order_data: List[OrderAggregateData],
        transaction_data: List[TransactionAggregateData]
    ):
        """计算业绩和实际出资"""
        for shop_data in shop_summary.values():
            # 实际出资 = abs(放款) + abs(供应商利润) + abs(成本) - (增值费 + 延保服务 + 首付款 + 租金 + 尾款)
            # 按照用户要求的新计算公式
            
            # 调试日志：记录计算过程
            income_part = abs(shop_data.loan_amount) + abs(shop_data.supplier_profit) + abs(shop_data.cost)
            expense_part = (shop_data.value_added_fee + shop_data.extended_warranty + 
                          shop_data.down_payment + shop_data.rent + shop_data.final_payment)
            
            logger.info(f"店铺 {shop_data.shop_name} 实际出资计算:")
            logger.info(f"  放款: {shop_data.loan_amount}, abs值: {abs(shop_data.loan_amount)}")
            logger.info(f"  供应商利润: {shop_data.supplier_profit}, abs值: {abs(shop_data.supplier_profit)}")
            logger.info(f"  成本: {shop_data.cost}, abs值: {abs(shop_data.cost)}")
            logger.info(f"  收入部分合计: {income_part}")
            logger.info(f"  支出部分: 增值费{shop_data.value_added_fee} + 延保{shop_data.extended_warranty} + 首付{shop_data.down_payment} + 租金{shop_data.rent} + 尾款{shop_data.final_payment} = {expense_part}")
            
            shop_data.actual_investment = income_part - expense_part
            logger.info(f"  最终实际出资: {income_part} - {expense_part} = {shop_data.actual_investment}")
    
    def _calculate_total_platform(self, shop_summary: Dict[str, ShopSummaryData]) -> ShopSummaryData:
        """计算总平台数据 - 修复成本重复计算问题"""
        total = ShopSummaryData(shop_name="总平台")
        
        for shop_data in shop_summary.values():
            total.total_devices += shop_data.total_devices
            total.total_receivable += shop_data.total_receivable
            total.lease_receivable += shop_data.lease_receivable
            total.ecommerce_receivable += shop_data.ecommerce_receivable
            total.value_added_fee += shop_data.value_added_fee
            total.extended_warranty += shop_data.extended_warranty
            total.down_payment += shop_data.down_payment
            total.rent += shop_data.rent
            total.final_payment += shop_data.final_payment
            total.loan_amount += shop_data.loan_amount
            total.reinvestment += shop_data.reinvestment
            total.supplier_profit += shop_data.supplier_profit
            # 注意：不累加各店铺成本（店铺成本是放款金额），真实业务成本由service层设置
            # total.cost += shop_data.cost  # 注释掉，避免错误累加放款金额
            total.ecommerce_performance += shop_data.ecommerce_performance
            total.lease_performance += shop_data.lease_performance
            total.overdue_principal += shop_data.overdue_principal
            total.overdue_receivable += shop_data.overdue_receivable
            total.completed_orders += shop_data.completed_orders
            total.ecommerce_orders += shop_data.ecommerce_orders
            total.lease_orders += shop_data.lease_orders
            total.overdue_orders += shop_data.overdue_orders
        
        # 计算实际出资：按照用户要求的新公式
        # 实际出资 = abs(放款) + abs(供应商利润) + abs(成本) - (增值费 + 延保服务 + 首付款 + 租金 + 尾款)
        
        # 调试日志：记录总平台计算过程
        total_income_part = abs(total.loan_amount) + abs(total.supplier_profit) + abs(total.cost)
        total_expense_part = (total.value_added_fee + total.extended_warranty + 
                            total.down_payment + total.rent + total.final_payment)
        
        logger.info(f"总平台实际出资计算:")
        logger.info(f"  放款: {total.loan_amount}, abs值: {abs(total.loan_amount)}")
        logger.info(f"  供应商利润: {total.supplier_profit}, abs值: {abs(total.supplier_profit)}")
        logger.info(f"  成本: {total.cost}, abs值: {abs(total.cost)}")
        logger.info(f"  收入部分合计: {total_income_part}")
        logger.info(f"  支出部分: 增值费{total.value_added_fee} + 延保{total.extended_warranty} + 首付{total.down_payment} + 租金{total.rent} + 尾款{total.final_payment} = {total_expense_part}")
        
        total.actual_investment = total_income_part - total_expense_part
        logger.info(f"  最终总平台实际出资: {total_income_part} - {total_expense_part} = {total.actual_investment}")
        
        return total
    
    def format_summary_data(
        self,
        period_data: Dict[str, ShopSummaryData],
        cumulative_data: Dict[str, ShopSummaryData]
    ) -> Tuple[List[str], List[List]]:
        """格式化汇总数据为API响应格式"""
        headers = self.config.HEADERS.copy()
        summary_data = []
        
        # 添加周期数据
        for shop_name in ["总平台"] + self.config.SHOP_NAMES:
            if shop_name in period_data:
                summary_data.append(period_data[shop_name].to_list())
        
        # 添加累计数据（带"累计 "前缀）
        for shop_name in ["总平台"] + self.config.SHOP_NAMES:
            if shop_name in cumulative_data:
                cumulative_data[shop_name].shop_name = f"累计 {shop_name}"
                summary_data.append(cumulative_data[shop_name].to_list())
        
        # 去重和排序
        unique_data = self._remove_duplicates_and_sort(summary_data)
        
        return headers, unique_data
    
    def _remove_duplicates_and_sort(self, summary_data: List[List]) -> List[List]:
        """去重和排序"""
        seen_shops = set()
        unique_data = []
        
        for row in summary_data:
            shop_name = row[0]
            if shop_name not in seen_shops:
                seen_shops.add(shop_name)
                unique_data.append(row)
        
        # 按预定义顺序排序
        unique_data.sort(key=lambda x: self.config.SHOP_ORDER.get(x[0], 999))
        
        return unique_data