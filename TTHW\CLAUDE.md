# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Flask API Project (`flask_api/`)
```bash
# Development
python run.py                    # Start development server (port 5000)

# Production
gunicorn -w 4 -b 0.0.0.0:5000 --timeout 300 --worker-class gthread --threads 2 --max-requests 1000 --max-requests-jitter 100 "run:app"

# Database Operations
python etl.py                    # Import Excel data to PostgreSQL database
python run_migrations.py         # Run database migrations
python migrate_to_postgresql.py  # Migrate from SQLite to PostgreSQL
python db_management.py init     # Initialize database tables
python scheduler.py              # Run scheduled tasks (payment status updates)

# Docker Operations
docker-compose up                # Production deployment with PostgreSQL
docker-compose -f docker-compose.dev.yml up  # Development deployment
```

### HDSC Query App (`hdsc_query_app_02/`)
```bash
# Development
python run.py                    # Start development server (port 5000)

# Frontend Build (Webpack)
npm run build                    # Production build (optimized)
npm run build:dev                # Development build  
npm run watch                    # Development with watch mode

# Production Deployment
gunicorn -c gunicorn_config.py run:app  # Use gunicorn config
docker-compose up -d             # Docker deployment

# Service Management (after deployment)
sudo systemctl start hdsc-query
sudo systemctl restart hdsc-query
sudo systemctl status hdsc-query
```

## High-Level Architecture

This repository contains two main Flask applications for Chinese financial data management:

### Flask API (`flask_api/`) - Order Management System
**Architecture**: Traditional Flask app with dual data persistence (Excel + PostgreSQL)

**Core Components**:
- **Data Layer**: SQLAlchemy ORM models with PostgreSQL backend, Excel file processing as legacy support
- **ETL Pipeline**: `etl.py` synchronizes Excel data (TTXW.xlsm) to PostgreSQL database
- **Scheduled Tasks**: Daily payment status updates, financial calculations via Flask-APScheduler
- **Route Pattern**: Database routes (`*_db.py`) and legacy Excel routes coexist for gradual migration

**Key Features**:
- PostgreSQL database with performance indexes and connection pooling
- API key authentication (`@require_api_key` decorator)
- Docker multi-platform support (ARM64/AMD64)
- Health monitoring endpoint (`/health`)

**Database Schema**:
- Order management with payment schedules
- Customer information tracking  
- Transaction history and financial calculations
- Automated overdue principal calculations

### HDSC Query System (`hdsc_query_app_02/`) - Financial Data Query Interface
**Architecture**: High-performance Flask app with external API integration and advanced caching

**Core Components**:
- **Proxy API Pattern**: No direct database, connects to external financial API (`*************:5000`)
- **Multi-tier Caching**: Flask-Caching + smart cache system with TTL-based expiration
- **Frontend Optimization**: Webpack 5 build system with 68% resource reduction
- **Modular JavaScript**: Organized in `core/`, `modules/`, `controllers/`, `pages/` with global namespace pattern

**Performance Achievements**:
- Page load time: 4.2s → 0.8s (81% improvement)
- Resource size: 3.8MB → 1.2MB (68% reduction)  
- Cache hit rate: 100% with intelligent preloading

**Authentication System**:
- Three-tier permissions: `TT2024` (limited), `881017` (standard), `Doolin` (full)
- File-based user management, no database required
- CAPTCHA protection with 8-hour session timeout

## Key Technical Patterns

### Data Access Patterns
**Flask API**: Direct database access via SQLAlchemy ORM with connection pooling
**HDSC Query**: External API proxy with intelligent caching and retry logic

### Route Organization
Both applications use Flask blueprints with auto-registration:
- Routes organized by functionality in `app/routes/`
- Consistent naming: `*_db.py` for database routes, `*_api.py` for external API routes
- Error handling with custom error pages in `templates/errors/`

### Caching Strategy (HDSC Query)
```python
# Multi-level caching with different TTLs
CACHE_TIMEOUTS = {
    'filters': 300,      # 5 minutes
    'orders': 600,       # 10 minutes  
    'summaries': 1800    # 30 minutes
}
```
- Scheduled cache prewarming at 2 AM daily via APScheduler
- Smart cache invalidation and memory management
- Session-based frontend caching with `sessionStorage`

### Build Systems
**Flask API**: Traditional Python deployment with Docker multi-stage builds
**HDSC Query**: Modern Webpack 5 build system:
- Code splitting with vendor/common chunks
- Terser minification with console removal
- CSS optimization with MiniCssExtractPlugin
- Performance budgets: 300KB max per asset

### Background Task Processing
Both applications use APScheduler for scheduled tasks:
- **Flask API**: Payment status updates, financial calculations, database maintenance
- **HDSC Query**: Cache prewarming, system maintenance, data cleanup

### Docker Strategy
**Multi-platform Support**: Both apps include ARM64 and AMD64 Docker builds
- Production: `docker-compose.yml` with PostgreSQL service (Flask API only)  
- Development: `docker-compose.dev.yml` with debugging enabled
- Health checks with curl-based monitoring

### Configuration Management
Environment-based configuration with `config.py` classes:
- `DevelopmentConfig`: DEBUG enabled, verbose logging
- `ProductionConfig`: Optimized for performance, minimal logging
- `TestingConfig`: Isolated test environment

Both applications detect environment via `FLASK_ENV` variable and configure accordingly.