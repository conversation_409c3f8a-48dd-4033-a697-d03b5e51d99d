# app/services/config.py
"""
业务配置类
包含各种业务规则和映射配置
"""

from typing import Dict, List


class SummaryConfig:
    """汇总数据配置"""
    
    # 表头定义
    HEADERS = [
        "店铺",
        "总台数",
        "总待收",
        "租赁待收",
        "电商待收",
        "增值费",
        "延保服务",
        "首付款",
        "租金",
        "尾款",
        "放款",
        "复投",
        "供应商利润",
        "成本",
        "电商业绩",
        "租赁业绩",
        "实际出资",
        "逾期本金",
        "逾期总待收",
        "已完成订单",
        "电商订单数",
        "租赁订单数",
        "逾期订单数",
    ]
    
    # 店铺名称定义
    SHOP_NAMES = ["太太租物", "涛涛好物", "刚刚好物", "林林租物", "太太享物"]
    
    # 产品类型映射
    PRODUCT_TYPE_MAPPING = {
        "租赁": ["租赁", "租", "租借"],
        "电商": ["电商", "电子商务", "网购"]
    }
    
    # 交易类型关键词映射
    TRANSACTION_TYPE_KEYWORDS = {
        "增值费": ["增值", "增值服务", "增值服务费"],
        "延保服务": ["延保", "延保服务"],
        "首付款": ["首付", "首付款"],
        "租金": ["租金"],
        "尾款": ["尾款"],
        "放款": ["放款"],
        "复投": ["复投"],
        "供应商利润": ["供应商", "供应商利润"],
        "成本": ["提成", "一次性支出", "固定支出", "风控充值", "服务器月租", "成本", "支出"]
    }
    
    # 支出类型交易（使用负数表示）
    EXPENSE_CATEGORIES = ["放款", "供应商利润", "成本"]
    
    # 订单状态定义
    ACTIVE_ORDER_STATUSES = ["在途", "逾期"]
    COMPLETED_ORDER_STATUS = "完结"
    OVERDUE_ORDER_STATUS = "逾期"
    
    # 还款成功状态
    COMPLETED_PAYMENT_STATUSES = ["按时还款", "协商结清", "逾期还款", "提前还款"]
    
    # 店铺排序顺序
    SHOP_ORDER = {
        "总平台": 0,
        "太太租物": 1,
        "涛涛好物": 2,
        "刚刚好物": 3,
        "林林租物": 4,
        "太太享物": 5,
        "累计 总平台": 6,
        "累计 太太租物": 7,
        "累计 涛涛好物": 8,
        "累计 刚刚好物": 9,
        "累计 林林租物": 10,
        "累计 太太享物": 11
    }
    
    # 逾期数据字段索引（用于原有逻辑兼容）
    OVERDUE_PRINCIPAL_INDEX = 17
    OVERDUE_RECEIVABLE_INDEX = 18
    OVERDUE_COUNT_INDEX = 22
    
    @classmethod
    def is_ecommerce_product(cls, product_type: str) -> bool:
        """判断是否为电商产品"""
        if not product_type:
            return False
        product_type_lower = product_type.lower().strip()
        return any(keyword in product_type_lower or product_type_lower in keyword 
                  for keyword in cls.PRODUCT_TYPE_MAPPING["电商"])
    
    @classmethod
    def get_transaction_category(cls, transaction_type: str) -> str:
        """获取交易类型分类"""
        if not transaction_type:
            return ""
        
        transaction_type_str = str(transaction_type).strip()
        for category, keywords in cls.TRANSACTION_TYPE_KEYWORDS.items():
            if any(keyword in transaction_type_str for keyword in keywords):
                return category
        return ""
    
    @classmethod
    def is_expense_category(cls, category: str) -> bool:
        """判断是否为支出类型"""
        return category in cls.EXPENSE_CATEGORIES