# app/routes/filter_data_db.py
# 使用数据库查询重构的日期筛选功能

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.routes.db.queries import OrderQueries
import logging
from datetime import datetime

bp = Blueprint('filter_data_db', __name__)


@bp.route('/filter_data_db', methods=['GET'])
@require_api_key('filter_data_db')
def filter_data_db():
    """
    根据日期筛选订单，并返回结果，包含客户信息补充（数据库版本）。
    """
    date_str = request.args.get('date')
    if not date_str:
        logging.warning("未提供筛选日期参数。")
        return jsonify({'error': '请提供筛选日期参数，如 ?date=YYYY-MM-DD'}), 400

    try:
        # 解析日期
        filter_date = datetime.strptime(date_str.strip(), "%Y-%m-%d").date()
    except ValueError:
        return jsonify({'error': '日期格式不正确，请使用 YYYY-MM-DD 格式。'}), 400

    try:
        # 使用数据库查询工具类筛选订单
        results = OrderQueries.filter_orders_by_date(filter_date)
        
        if not results:
            logging.info(f"未找到匹配的订单，筛选日期: {filter_date}")
            return jsonify({'message': '未找到匹配的订单。', 'results': []}), 200
            
        logging.info(f"成功筛选订单(DB版)，筛选日期: {filter_date}，找到 {len(results)} 条记录")
        return jsonify({'results': results})
    except Exception as e:
        logging.error(f'数据处理错误(DB版)：{str(e)}')
        logging.exception("详细错误信息")
        return jsonify({'error': '数据处理错误，请联系管理员。'}), 500
