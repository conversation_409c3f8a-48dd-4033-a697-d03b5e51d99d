{"version": 3, "names": ["t", "require", "_t", "_traverseNode", "_visitors", "_context", "getAssignmentIdentifiers", "renameVisitor", "ReferencedIdentifier", "node", "state", "name", "old<PERSON>ame", "newName", "<PERSON><PERSON>", "path", "scope", "bindingIdentifierEquals", "binding", "identifier", "skip", "isMethod", "requeueComputedKeyAndDecorators", "call", "ObjectProperty", "key", "shorthand", "getBindingIdentifier", "_node$extra", "extra", "AssignmentExpression|Declaration|VariableDeclarator", "isVariableDeclaration", "ids", "isAssignmentExpression", "getOuterBindingIdentifiers", "Renamer", "constructor", "maybeConvertFromExportDeclaration", "parentDeclar", "maybeExportDeclar", "parentPath", "isExportDeclaration", "isExportDefaultDeclaration", "declaration", "isDeclaration", "id", "isExportAllDeclaration", "splitExportDeclaration", "maybeConvertFromClassFunctionDeclaration", "maybeConvertFromClassFunctionExpression", "rename", "find", "isFunctionExpression", "isClassExpression", "bindingIds", "blockToTraverse", "arguments", "block", "<PERSON><PERSON><PERSON><PERSON>", "discriminant", "computed", "isObjectMethod", "decorators", "traverseNode", "explode", "removeOwnBinding", "bindings", "exports", "default"], "sources": ["../../../src/scope/lib/renamer.ts"], "sourcesContent": ["import type Binding from \"../binding.ts\";\nimport * as t from \"@babel/types\";\nimport type { NodePath, Visitor } from \"../../index.ts\";\nimport { traverseNode } from \"../../traverse-node.ts\";\nimport { explode } from \"../../visitors.ts\";\nimport { getAssignmentIdentifiers, type Identifier } from \"@babel/types\";\nimport { requeueComputedKeyAndDecorators } from \"../../path/context.ts\";\n\nconst renameVisitor: Visitor<Renamer> = {\n  ReferencedIdentifier({ node }, state) {\n    if (node.name === state.oldName) {\n      node.name = state.newName;\n    }\n  },\n\n  Scope(path, state) {\n    if (\n      !path.scope.bindingIdentifierEquals(\n        state.oldName,\n        state.binding.identifier,\n      )\n    ) {\n      path.skip();\n      if (path.isMethod()) {\n        if (\n          !process.env.BABEL_8_BREAKING &&\n          !path.requeueComputedKeyAndDecorators\n        ) {\n          // See https://github.com/babel/babel/issues/16694\n          requeueComputedKeyAndDecorators.call(path);\n        } else {\n          path.requeueComputedKeyAndDecorators();\n        }\n      }\n    }\n  },\n\n  ObjectProperty({ node, scope }, state) {\n    const { name } = node.key as Identifier;\n    if (\n      node.shorthand &&\n      // In destructuring the identifier is already renamed by the\n      // AssignmentExpression|Declaration|VariableDeclarator visitor,\n      // while in object literals it's renamed later by the\n      // ReferencedIdentifier visitor.\n      (name === state.oldName || name === state.newName) &&\n      // Ignore shadowed bindings\n      scope.getBindingIdentifier(name) === state.binding.identifier\n    ) {\n      node.shorthand = false;\n      if (!process.env.BABEL_8_BREAKING) {\n        if (node.extra?.shorthand) node.extra.shorthand = false;\n      }\n    }\n  },\n\n  \"AssignmentExpression|Declaration|VariableDeclarator\"(\n    path: NodePath<\n      t.AssignmentExpression | t.Declaration | t.VariableDeclarator\n    >,\n    state,\n  ) {\n    if (path.isVariableDeclaration()) return;\n    const ids = path.isAssignmentExpression()\n      ? // See https://github.com/babel/babel/issues/16694\n        getAssignmentIdentifiers(path.node)\n      : path.getOuterBindingIdentifiers();\n\n    for (const name in ids) {\n      if (name === state.oldName) ids[name].name = state.newName;\n    }\n  },\n};\n\nexport default class Renamer {\n  constructor(binding: Binding, oldName: string, newName: string) {\n    this.newName = newName;\n    this.oldName = oldName;\n    this.binding = binding;\n  }\n\n  declare oldName: string;\n  declare newName: string;\n  declare binding: Binding;\n\n  maybeConvertFromExportDeclaration(parentDeclar: NodePath) {\n    const maybeExportDeclar = parentDeclar.parentPath;\n\n    if (!maybeExportDeclar.isExportDeclaration()) {\n      return;\n    }\n\n    if (maybeExportDeclar.isExportDefaultDeclaration()) {\n      const { declaration } = maybeExportDeclar.node;\n      if (t.isDeclaration(declaration) && !declaration.id) {\n        return;\n      }\n    }\n\n    if (maybeExportDeclar.isExportAllDeclaration()) {\n      return;\n    }\n\n    maybeExportDeclar.splitExportDeclaration();\n  }\n\n  maybeConvertFromClassFunctionDeclaration(path: NodePath) {\n    return path; // TODO\n\n    // // retain the `name` of a class/function declaration\n\n    // if (!path.isFunctionDeclaration() && !path.isClassDeclaration()) return;\n    // if (this.binding.kind !== \"hoisted\") return;\n\n    // path.node.id = identifier(this.oldName);\n    // path.node._blockHoist = 3;\n\n    // path.replaceWith(\n    //   variableDeclaration(\"let\", [\n    //     variableDeclarator(identifier(this.newName), toExpression(path.node)),\n    //   ]),\n    // );\n  }\n\n  maybeConvertFromClassFunctionExpression(path: NodePath) {\n    return path; // TODO\n\n    // // retain the `name` of a class/function expression\n\n    // if (!path.isFunctionExpression() && !path.isClassExpression()) return;\n    // if (this.binding.kind !== \"local\") return;\n\n    // path.node.id = identifier(this.oldName);\n\n    // this.binding.scope.parent.push({\n    //   id: identifier(this.newName),\n    // });\n\n    // path.replaceWith(\n    //   assignmentExpression(\"=\", identifier(this.newName), path.node),\n    // );\n  }\n\n  rename(/* Babel 7 - block?: t.Pattern | t.Scopable */) {\n    const { binding, oldName, newName } = this;\n    const { scope, path } = binding;\n\n    const parentDeclar = path.find(\n      path =>\n        path.isDeclaration() ||\n        path.isFunctionExpression() ||\n        path.isClassExpression(),\n    );\n    if (parentDeclar) {\n      const bindingIds = parentDeclar.getOuterBindingIdentifiers();\n      if (bindingIds[oldName] === binding.identifier) {\n        // When we are renaming an exported identifier, we need to ensure that\n        // the exported binding keeps the old name.\n        this.maybeConvertFromExportDeclaration(parentDeclar);\n      }\n    }\n\n    const blockToTraverse = process.env.BABEL_8_BREAKING\n      ? scope.block\n      : (arguments[0] as t.Pattern | t.Scopable) || scope.block;\n\n    // When blockToTraverse is a SwitchStatement, the discriminant\n    // is not part of the current scope and thus should be skipped.\n\n    // const foo = {\n    //   get [x]() {\n    //     return x;\n    //   },\n    // };\n    const skipKeys: Record<string, true> = { discriminant: true };\n    if (t.isMethod(blockToTraverse)) {\n      if (blockToTraverse.computed) {\n        skipKeys.key = true;\n      }\n      if (!t.isObjectMethod(blockToTraverse)) {\n        skipKeys.decorators = true;\n      }\n    }\n\n    traverseNode(\n      blockToTraverse,\n      explode(renameVisitor),\n      scope,\n      this,\n      scope.path,\n      skipKeys,\n    );\n\n    if (process.env.BABEL_8_BREAKING) {\n      scope.removeOwnBinding(oldName);\n      scope.bindings[newName] = binding;\n      this.binding.identifier.name = newName;\n    } else if (!arguments[0]) {\n      scope.removeOwnBinding(oldName);\n      scope.bindings[newName] = binding;\n      this.binding.identifier.name = newName;\n    }\n\n    if (parentDeclar) {\n      this.maybeConvertFromClassFunctionDeclaration(path);\n      this.maybeConvertFromClassFunctionExpression(path);\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,CAAA,GAAAC,OAAA;AAAkC,IAAAC,EAAA,GAAAF,CAAA;AAElC,IAAAG,aAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAJ,OAAA;AAAwE;EAD/DK;AAAwB,IAAAJ,EAAA;AAGjC,MAAMK,aAA+B,GAAG;EACtCC,oBAAoBA,CAAC;IAAEC;EAAK,CAAC,EAAEC,KAAK,EAAE;IACpC,IAAID,IAAI,CAACE,IAAI,KAAKD,KAAK,CAACE,OAAO,EAAE;MAC/BH,IAAI,CAACE,IAAI,GAAGD,KAAK,CAACG,OAAO;IAC3B;EACF,CAAC;EAEDC,KAAKA,CAACC,IAAI,EAAEL,KAAK,EAAE;IACjB,IACE,CAACK,IAAI,CAACC,KAAK,CAACC,uBAAuB,CACjCP,KAAK,CAACE,OAAO,EACbF,KAAK,CAACQ,OAAO,CAACC,UAChB,CAAC,EACD;MACAJ,IAAI,CAACK,IAAI,CAAC,CAAC;MACX,IAAIL,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAE;QACnB,IAEE,CAACN,IAAI,CAACO,+BAA+B,EACrC;UAEAA,wCAA+B,CAACC,IAAI,CAACR,IAAI,CAAC;QAC5C,CAAC,MAAM;UACLA,IAAI,CAACO,+BAA+B,CAAC,CAAC;QACxC;MACF;IACF;EACF,CAAC;EAEDE,cAAcA,CAAC;IAAEf,IAAI;IAAEO;EAAM,CAAC,EAAEN,KAAK,EAAE;IACrC,MAAM;MAAEC;IAAK,CAAC,GAAGF,IAAI,CAACgB,GAAiB;IACvC,IACEhB,IAAI,CAACiB,SAAS,KAKbf,IAAI,KAAKD,KAAK,CAACE,OAAO,IAAID,IAAI,KAAKD,KAAK,CAACG,OAAO,CAAC,IAElDG,KAAK,CAACW,oBAAoB,CAAChB,IAAI,CAAC,KAAKD,KAAK,CAACQ,OAAO,CAACC,UAAU,EAC7D;MACAV,IAAI,CAACiB,SAAS,GAAG,KAAK;MACa;QAAA,IAAAE,WAAA;QACjC,KAAAA,WAAA,GAAInB,IAAI,CAACoB,KAAK,aAAVD,WAAA,CAAYF,SAAS,EAAEjB,IAAI,CAACoB,KAAK,CAACH,SAAS,GAAG,KAAK;MACzD;IACF;EACF,CAAC;EAED,qDAAqDI,CACnDf,IAEC,EACDL,KAAK,EACL;IACA,IAAIK,IAAI,CAACgB,qBAAqB,CAAC,CAAC,EAAE;IAClC,MAAMC,GAAG,GAAGjB,IAAI,CAACkB,sBAAsB,CAAC,CAAC,GAErC3B,wBAAwB,CAACS,IAAI,CAACN,IAAI,CAAC,GACnCM,IAAI,CAACmB,0BAA0B,CAAC,CAAC;IAErC,KAAK,MAAMvB,IAAI,IAAIqB,GAAG,EAAE;MACtB,IAAIrB,IAAI,KAAKD,KAAK,CAACE,OAAO,EAAEoB,GAAG,CAACrB,IAAI,CAAC,CAACA,IAAI,GAAGD,KAAK,CAACG,OAAO;IAC5D;EACF;AACF,CAAC;AAEc,MAAMsB,OAAO,CAAC;EAC3BC,WAAWA,CAAClB,OAAgB,EAAEN,OAAe,EAAEC,OAAe,EAAE;IAC9D,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,OAAO,GAAGA,OAAO;EACxB;EAMAmB,iCAAiCA,CAACC,YAAsB,EAAE;IACxD,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,UAAU;IAEjD,IAAI,CAACD,iBAAiB,CAACE,mBAAmB,CAAC,CAAC,EAAE;MAC5C;IACF;IAEA,IAAIF,iBAAiB,CAACG,0BAA0B,CAAC,CAAC,EAAE;MAClD,MAAM;QAAEC;MAAY,CAAC,GAAGJ,iBAAiB,CAAC9B,IAAI;MAC9C,IAAIT,CAAC,CAAC4C,aAAa,CAACD,WAAW,CAAC,IAAI,CAACA,WAAW,CAACE,EAAE,EAAE;QACnD;MACF;IACF;IAEA,IAAIN,iBAAiB,CAACO,sBAAsB,CAAC,CAAC,EAAE;MAC9C;IACF;IAEAP,iBAAiB,CAACQ,sBAAsB,CAAC,CAAC;EAC5C;EAEAC,wCAAwCA,CAACjC,IAAc,EAAE;IACvD,OAAOA,IAAI;EAeb;EAEAkC,uCAAuCA,CAAClC,IAAc,EAAE;IACtD,OAAOA,IAAI;EAgBb;EAEAmC,MAAMA,CAAA,EAAiD;IACrD,MAAM;MAAEhC,OAAO;MAAEN,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC1C,MAAM;MAAEG,KAAK;MAAED;IAAK,CAAC,GAAGG,OAAO;IAE/B,MAAMoB,YAAY,GAAGvB,IAAI,CAACoC,IAAI,CAC5BpC,IAAI,IACFA,IAAI,CAAC6B,aAAa,CAAC,CAAC,IACpB7B,IAAI,CAACqC,oBAAoB,CAAC,CAAC,IAC3BrC,IAAI,CAACsC,iBAAiB,CAAC,CAC3B,CAAC;IACD,IAAIf,YAAY,EAAE;MAChB,MAAMgB,UAAU,GAAGhB,YAAY,CAACJ,0BAA0B,CAAC,CAAC;MAC5D,IAAIoB,UAAU,CAAC1C,OAAO,CAAC,KAAKM,OAAO,CAACC,UAAU,EAAE;QAG9C,IAAI,CAACkB,iCAAiC,CAACC,YAAY,CAAC;MACtD;IACF;IAEA,MAAMiB,eAAe,GAEhBC,SAAS,CAAC,CAAC,CAAC,IAA+BxC,KAAK,CAACyC,KAAK;IAU3D,MAAMC,QAA8B,GAAG;MAAEC,YAAY,EAAE;IAAK,CAAC;IAC7D,IAAI3D,CAAC,CAACqB,QAAQ,CAACkC,eAAe,CAAC,EAAE;MAC/B,IAAIA,eAAe,CAACK,QAAQ,EAAE;QAC5BF,QAAQ,CAACjC,GAAG,GAAG,IAAI;MACrB;MACA,IAAI,CAACzB,CAAC,CAAC6D,cAAc,CAACN,eAAe,CAAC,EAAE;QACtCG,QAAQ,CAACI,UAAU,GAAG,IAAI;MAC5B;IACF;IAEA,IAAAC,0BAAY,EACVR,eAAe,EACf,IAAAS,iBAAO,EAACzD,aAAa,CAAC,EACtBS,KAAK,EACL,IAAI,EACJA,KAAK,CAACD,IAAI,EACV2C,QACF,CAAC;IAMM,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC,EAAE;MACxBxC,KAAK,CAACiD,gBAAgB,CAACrD,OAAO,CAAC;MAC/BI,KAAK,CAACkD,QAAQ,CAACrD,OAAO,CAAC,GAAGK,OAAO;MACjC,IAAI,CAACA,OAAO,CAACC,UAAU,CAACR,IAAI,GAAGE,OAAO;IACxC;IAEA,IAAIyB,YAAY,EAAE;MAChB,IAAI,CAACU,wCAAwC,CAACjC,IAAI,CAAC;MACnD,IAAI,CAACkC,uCAAuC,CAAClC,IAAI,CAAC;IACpD;EACF;AACF;AAACoD,OAAA,CAAAC,OAAA,GAAAjC,OAAA", "ignoreList": []}