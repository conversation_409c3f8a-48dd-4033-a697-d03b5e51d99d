# app/routes/db/__init__.py
# 数据库访问层初始化

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()
DB_URI = os.getenv('DATABASE_URI', 'postgresql://flask_user:flask_password@localhost:5433/flask_db')  # 默认 PostgreSQL 数据库

# 创建数据库引擎
engine = create_engine(DB_URI, echo=False)

# 创建会话工厂
session_factory = sessionmaker(bind=engine)

# 创建线程安全的会话
Session = scoped_session(session_factory)

def get_db_session():
    """获取数据库会话"""
    return Session()

def close_db_session(session):
    """关闭数据库会话"""
    if session:
        session.close()
