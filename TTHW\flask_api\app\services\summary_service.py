# app/services/summary_service.py
"""
汇总数据服务层
协调数据访问层和计算层，实现完整的业务流程
"""

import logging
import time
from datetime import date
from typing import Tuple, List, Dict

from app.services.dto import SummaryRequest, SummaryResponse
from app.utils.date_parser import parse_date
from app.services.summary_repository import SummaryRepository
from app.services.summary_calculator import SummaryCalculator
from app.services.config import SummaryConfig

logger = logging.getLogger(__name__)


class SummaryService:
    """汇总数据服务"""
    
    def __init__(self):
        self.repository = SummaryRepository()
        self.calculator = SummaryCalculator()
        self.config = SummaryConfig
    
    def get_summary_data(self, request: SummaryRequest) -> SummaryResponse:
        """
        获取汇总数据的主要入口方法
        实现周期数据 + 累计数据的合并逻辑
        """
        start_time = time.time()
        logger.info("======================== 数据汇总服务开始 ========================")
        logger.info(f"请求参数: {request.start_date} 至 {request.end_date}")
        
        timing_stats = {}
        
        try:
            # 1. 获取最早订单日期
            earliest_date_start = time.time()
            earliest_date = self.repository.get_earliest_order_date()
            if earliest_date is None:
                earliest_date = request.start_date
            timing_stats["获取最早日期"] = f"{time.time() - earliest_date_start:.4f}秒"
            
            # 2. 并行计算周期数据和累计数据
            period_start = time.time()
            period_data = self._calculate_period_summary(request.start_date, request.end_date)
            timing_stats["周期数据计算"] = f"{time.time() - period_start:.4f}秒"
            
            cumulative_start = time.time()
            cumulative_data = self._calculate_cumulative_summary(earliest_date, request.end_date)
            timing_stats["累计数据计算"] = f"{time.time() - cumulative_start:.4f}秒"
            
            # 3. 处理逾期数据
            overdue_start = time.time()
            overdue_data = self.repository.get_overdue_data()
            
            # 应用逾期数据到周期和累计数据
            period_data = self.calculator.apply_overdue_data(period_data, overdue_data)
            cumulative_data = self.calculator.apply_overdue_data(cumulative_data, overdue_data)
            timing_stats["逾期数据处理"] = f"{time.time() - overdue_start:.4f}秒"
            
            # 4. 格式化输出
            format_start = time.time()
            headers, summary_data = self.calculator.format_summary_data(period_data, cumulative_data)
            timing_stats["数据格式化"] = f"{time.time() - format_start:.4f}秒"
            
            # 5. 计算总耗时
            total_duration = time.time() - start_time
            timing_stats["总耗时"] = f"{total_duration:.4f}秒"
            
            logger.info(f"数据汇总服务完成，总耗时: {total_duration:.4f}秒")
            logger.info("======================== 数据汇总服务结束 ========================")
            
            return SummaryResponse(
                headers=headers,
                summary=summary_data,
                timing_stats=timing_stats
            )
            
        except Exception as e:
            error_duration = time.time() - start_time
            logger.error(f"数据汇总服务异常: {str(e)}")
            logger.error(f"异常处理总耗时: {error_duration:.4f}秒")
            raise
    
    def _calculate_period_summary(self, start_date: date, end_date: date) -> Dict:
        """计算周期汇总数据"""
        logger.info(f"开始计算周期汇总: {start_date} 至 {end_date}")
        
        # 获取周期内的数据
        order_data = self.repository.get_aggregated_order_data(start_date, end_date)
        transaction_data = self.repository.get_aggregated_transaction_data(start_date, end_date)
        unassigned_cost = self.repository.get_unassigned_cost_transactions(start_date, end_date)
        
        # 对于周期查询，需要特殊的还款数据处理
        period_repayments = self.repository.get_period_repayment_data(start_date, end_date)
        
        # 计算周期汇总
        period_summary = self.calculator.calculate_period_summary(
            order_data, transaction_data, unassigned_cost, period_repayments
        )
        
        # 周期查询的特殊逻辑：调整待收金额
        period_summary = self._adjust_period_receivables(
            period_summary, order_data, period_repayments, start_date, end_date, unassigned_cost
        )
        
        logger.info("周期汇总计算完成")
        return period_summary
    
    def _calculate_cumulative_summary(self, start_date: date, end_date: date) -> Dict:
        """计算累计汇总数据"""
        logger.info(f"开始计算累计汇总: {start_date} 至 {end_date}")
        
        # 获取累计数据
        order_data = self.repository.get_cumulative_order_data(end_date)
        transaction_data = self.repository.get_cumulative_transaction_data(end_date)
        unassigned_cost = self.repository.get_cumulative_unassigned_cost(end_date)
        
        logger.info(f"累计数据获取完成，未分配成本: {unassigned_cost}")
        
        # 计算累计汇总
        cumulative_summary = self.calculator.calculate_cumulative_summary(
            order_data, transaction_data, unassigned_cost
        )
        
        # 修正总平台成本：总平台成本应仅为所有真实业务成本的合计（绝对值）
        if "总平台" in cumulative_summary:
            # 获取所有真实业务成本（包括有归属和无归属的）
            total_business_cost = self.repository.get_total_business_cost(end_date)
            cumulative_summary["总平台"].cost = abs(total_business_cost)
            logger.info(f"总平台成本计算：真实业务成本合计={total_business_cost}, 绝对值={abs(total_business_cost)}")
            
            # 重新计算总平台实际出资，使用修正后的成本值
            total_platform = cumulative_summary["总平台"]
            total_income_part = abs(total_platform.loan_amount) + abs(total_platform.supplier_profit) + abs(total_platform.cost)
            total_expense_part = (total_platform.value_added_fee + total_platform.extended_warranty + 
                                total_platform.down_payment + total_platform.rent + total_platform.final_payment)
            total_platform.actual_investment = total_income_part - total_expense_part
            
            logger.info(f"重新计算总平台实际出资:")
            logger.info(f"  收入部分: abs({total_platform.loan_amount}) + abs({total_platform.supplier_profit}) + abs({total_platform.cost}) = {total_income_part}")
            logger.info(f"  支出部分: {total_expense_part}")
            logger.info(f"  最终实际出资: {total_income_part} - {total_expense_part} = {total_platform.actual_investment}")
        
        logger.info("累计汇总计算完成")
        return cumulative_summary
    
    def _adjust_period_receivables(
        self, 
        period_summary: Dict, 
        order_data: List, 
        period_repayments: Dict,
        start_date: date,
        end_date: date,
        unassigned_cost
    ) -> Dict:
        """
        调整周期查询的待收金额
        实现原有逻辑：总应收 - 时间段内还款 = 实际待收
        """
        logger.info("开始调整周期待收金额")
        
        # 重新计算每个店铺的待收金额
        for shop_name, shop_data in period_summary.items():
            if shop_name == "总平台":
                continue
                
            # 获取该店铺在时间段内的订单
            shop_orders = [order for order in order_data 
                          if order.shop_affiliation == shop_name 
                          and order.status in self.config.ACTIVE_ORDER_STATUSES]
            
            lease_receivable = 0
            ecommerce_receivable = 0
            
            for order in shop_orders:
                # 获取订单在时间段内的还款
                order_repayment = period_repayments.get(order.shop_affiliation, 0)  # 这里需要order_id，但聚合数据没有
                
                # 计算实际待收 = 总应收 - 时间段内还款
                actual_receivable = max(0, order.total_receivable - order_repayment)
                
                # 按产品类型分类
                if self.config.is_ecommerce_product(order.product_type):
                    ecommerce_receivable += actual_receivable
                else:
                    lease_receivable += actual_receivable
            
            # 由于聚合查询的限制，这里简化处理
            # 在实际应用中，可能需要更精确的订单级别计算
            logger.info(f"店铺 {shop_name} 周期待收调整完成")
        
        # 重新计算总平台数据，并正确处理未分配成本
        total_platform = self.calculator._calculate_total_platform(
            {k: v for k, v in period_summary.items() if k != "总平台"}
        )
        
        # 添加未分配成本到总平台：总平台成本已包含各店铺成本合计，只需加上未分配成本
        total_platform.cost = total_platform.cost + abs(unassigned_cost)
        
        period_summary["总平台"] = total_platform
        
        logger.info("周期待收金额调整完成")
        return period_summary
    
    def validate_request(self, start_date_str: str, end_date_str: str) -> Tuple[SummaryRequest, str]:
        """
        验证请求参数
        
        Returns:
            Tuple[SummaryRequest, str]: (请求对象, 错误信息)
        """
        
        try:
            start_date = parse_date(start_date_str.strip(), "开始日期")
            if start_date is None:
                return None, "开始日期格式不正确，请使用 YYYY-MM-DD 格式。"
            
            end_date = parse_date(end_date_str.strip(), "结束日期")
            if end_date is None:
                return None, "结束日期格式不正确，请使用 YYYY-MM-DD 格式。"
            
            request = SummaryRequest(start_date=start_date, end_date=end_date)
            error = request.validate()
            
            return request, error
            
        except Exception as e:
            return None, f"日期解析错误: {str(e)}"
    
    def get_legacy_summary_data(self, start_date: date, end_date: date, is_cumulative: bool = False) -> Tuple[List[str], List[List]]:
        """
        获取与原有接口兼容的汇总数据
        为了保持向后兼容性而提供的方法
        """
        if is_cumulative:
            # 累计查询：从最早日期到结束日期
            earliest_date = self.repository.get_earliest_order_date()
            if earliest_date is None:
                earliest_date = start_date
            summary_data = self._calculate_cumulative_summary(earliest_date, end_date)
        else:
            # 周期查询
            summary_data = self._calculate_period_summary(start_date, end_date)
        
        # 转换为原有格式
        headers = self.config.HEADERS.copy()
        formatted_data = []
        
        # 按原有顺序添加数据
        for shop_name in ["总平台"] + self.config.SHOP_NAMES:
            if shop_name in summary_data:
                formatted_data.append(summary_data[shop_name].to_list())
        
        return headers, formatted_data