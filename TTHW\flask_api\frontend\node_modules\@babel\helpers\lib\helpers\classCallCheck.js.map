{"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError"], "sources": ["../../src/helpers/classCallCheck.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _classCallCheck<T extends object>(\n  instance: unknown,\n  Constructor: new (...args: any[]) => T,\n): asserts instance is T {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n"], "mappings": ";;;;;;AAEe,SAASA,eAAeA,CACrCC,QAAiB,EACjBC,WAAsC,EACf;EACvB,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF", "ignoreList": []}